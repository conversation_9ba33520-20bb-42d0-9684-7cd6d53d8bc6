import 'dart:async';

import 'package:flutter/material.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_detail_response.dart';
import 'package:turing_art/datalayer/repository/aigc_presets/aigc_presets_repository.dart';
import 'package:turing_art/providers/aigc_preset_detail_polling_provider.dart';
import 'package:turing_art/ui/aigc_presets/config/aigc_config.dart';
import 'package:turing_art/ui/aigc_presets/model/aigc_preset_detail_ui_model.dart';
import 'package:turing_art/ui/core/base_view_model.dart';
import 'package:turing_art/utils/error_handler.dart';
import 'package:turing_art/utils/pg_log.dart';

/// AIGC预设详情ViewModel
class AIGCPresetDetailViewModel extends BaseViewModel {
  final AigcPresetsRepository _repository;
  final String _presetId;
  final AigcPresetDetailPollingProvider _pollingProvider;
  StreamSubscription<AigcPcPresetsDetailResponse>? _pollingResultSubscription;

  bool _isLoading = false;

  // Effects 照片列表状态是否发生变化，用于判断是否需要通知外层刷新数据
  bool _hasEffectChanged = false;

  // 原始的预设详情数据
  AigcPcPresetsDetailResponse? _rawResponse;

  // 用于预设详情的 UI 模型
  AIGCPresetDetailUI? _presetDetail;

  bool get hasEffectChanged => _hasEffectChanged;

  String? _errorMessage;
  final int _regenerateCount = AigcConfig.defaultGenerateCount; // 再次生成的张数

  String get presetId => _presetId;

  AIGCPresetDetailUI? get presetDetail => _presetDetail;

  List<AIGCPresetEffectUI> get effects =>
      _presetDetail?.effects.expand((element) => element.effectList).toList() ??
      [];

  List<AIGCPresetGroupUI> get groups => _presetDetail?.effects ?? [];

  bool get isLoading => _isLoading;

  String? get errorMessage => _errorMessage;

  /// 获取总数量
  int get totalCount => _presetDetail?.totalCount ?? 0;

  /// 获取已选中数量
  int get selectedCount => _presetDetail?.selectedCount ?? 0;

  /// 获取再次生成的张数
  int get regenerateCount => _regenerateCount;

  /// 计算再次生成需要消耗的 credit 数量
  int get regenerateRequiredCredits =>
      AigcConfig.calculateRequiredCredits(_regenerateCount);

  AIGCPresetDetailViewModel({
    required AigcPresetsRepository repository,
    required String presetId,
    required AigcPresetDetailPollingProvider pollingService,
    AigcPcPresetsDetailResponse? initialData,
  })  : _repository = repository,
        _presetId = presetId,
        _pollingProvider = pollingService,
        super(ErrorHandler()) {
    _listenToPollingResults();
    if (initialData != null) {
      _initializeWithData(initialData);
    } else {
      _loadPresetDetail();
    }
  }

  /// 使用初始数据初始化
  void _initializeWithData(AigcPcPresetsDetailResponse data) {
    _rawResponse = data;
    _presetDetail = AIGCPresetDetailUI.fromPresetDetailResponse(data);
    _checkAndHandlePolling(data);

    // 初始化时需要通知监听器
    WidgetsBinding.instance.addPostFrameCallback((_) {
      notifyListeners();
    });
  }

  /// 设置加载状态
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

  /// 设置异常错误消息
  void _setErrorFromException(Exception exception, [String? fallbackMessage]) {
    final errorMessage = handleException(exception, fallbackMessage);
    _setErrorMessage(errorMessage);
  }

  /// 设置错误消息
  void _setErrorMessage(String? message) {
    if (_errorMessage != message) {
      _errorMessage = message;
      notifyListeners();
    }
  }

  /// 更新预设详情
  void _updatePresetDetail(AIGCPresetDetailUI? newDetail) {
    if (_presetDetail != newDetail) {
      _presetDetail = newDetail;
      notifyListeners();
    }
  }

  /// 加载预设详情
  Future<void> _loadPresetDetail() async {
    _setLoading(true);
    _setErrorMessage(null);

    try {
      final response = await _repository.getAigcPresetsDetail(_presetId);
      _rawResponse = response;
      final newPresetDetail =
          AIGCPresetDetailUI.fromPresetDetailResponse(response);
      _updatePresetDetail(newPresetDetail);
      _checkAndHandlePolling(response);
    } catch (e) {
      _setErrorFromException(
          e is Exception ? e : Exception(e.toString()), '加载预设详情失败，请稍后重试');
      PGLog.e('Failed to load preset detail: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// 切换创意项目的选中状态
  void toggleCreativeItemSelection(String groupId, String effectCode) {
    if (_presetDetail == null) {
      return;
    }

    final updatedDetail =
        _presetDetail!.toggleEffectSelectionAt(groupId, effectCode);
    _updatePresetDetail(updatedDetail);
  }

  /// 删除单张效果图
  Future<void> deleteEffect(String groupId, String effectCode) async {
    if (_presetDetail == null) {
      return;
    }

    try {
      await _repository.deleteAigcPresetsEffect(_presetId, [effectCode]);
      _hasEffectChanged = true;

      // 删除成功，更新UI模型
      final updatedDetail = _presetDetail!.removeEffectAt(groupId, effectCode);
      _updatePresetDetail(updatedDetail);
    } catch (e) {
      _setErrorFromException(
          e is Exception ? e : Exception(e.toString()), '删除创意效果失败，请稍后重试');
      PGLog.e('Failed to delete creative item: $e');
    }
  }

  /// 删除整个预设主题
  Future<void> deletePreset() async {
    try {
      await _repository.deleteAigcPresets(_presetId);
      // 删除成功后，可以通过回调通知上级页面
    } catch (e) {
      _setErrorFromException(
          e is Exception ? e : Exception(e.toString()), '删除主题失败，请稍后重试');
      PGLog.e('Failed to delete preset: $e');
    }
  }

  /// 确认选择的创意效果
  Future<void> confirmSelection() async {
    if (_presetDetail == null) {
      return;
    }

    try {
      final selectedEffectCodes = _presetDetail!.selectedEffectCodes;
      await _repository.selectAigcPresetsEffect(_presetId, selectedEffectCodes);
      _hasEffectChanged = true;
      // 选择成功后，可以通过回调通知上级页面
    } catch (e) {
      _setErrorFromException(
          e is Exception ? e : Exception(e.toString()), '确认选择失败，请稍后重试');
      PGLog.e('Failed to confirm selection: $e');
    }
  }

  /// 清除错误消息
  void clearError() {
    _setErrorMessage(null);
  }

  /// 使用新的响应数据更新详情
  void updateWithNewData(AigcPcPresetsDetailResponse response) {
    _handleNewData(response);
    _hasEffectChanged = true; // 标记数据已变化
    _checkAndHandlePolling(response);
  }

  /// 监听轮询结果
  void _listenToPollingResults() {
    _pollingResultSubscription = _pollingProvider.pollingResultStream
        .listen(_onPollResult, onError: (e) {
      PGLog.e('Error from polling stream: $e');
    });
  }

  /// 根据响应状态检查并处理轮询
  void _checkAndHandlePolling(AigcPcPresetsDetailResponse response) {
    if (response.isInRunningStatus()) {
      _pollingProvider.startPolling();
    } else {
      _pollingProvider.stopPolling();
    }
  }

  /// 轮询结果处理器
  void _onPollResult(AigcPcPresetsDetailResponse newResponse) {
    PGLog.d('轮询预设详情成功，状态: ${newResponse.status}');
    // 只有当数据发生变化时才更新
    if (newResponse != _rawResponse) {
      PGLog.d('预设详情数据有变化，刷新 UI');
      _handleNewData(newResponse);
    }
  }

  /// 处理新的数据，同时保留 UI 状态
  void _handleNewData(AigcPcPresetsDetailResponse response) {
    if (_presetDetail == null) {
      // 如果当前没有数据，直接使用新数据
      _updatePresetDetail(
          AIGCPresetDetailUI.fromPresetDetailResponse(response));
    } else {
      // 使用模型方法更新，同时保留选中状态
      final newDetail = _presetDetail!.copyWithNewResponse(response);
      _updatePresetDetail(newDetail);
    }

    _hasEffectChanged = true;
    _rawResponse = response;
  }

  @override
  void dispose() {
    _pollingResultSubscription?.cancel();
    _pollingProvider.dispose();
    super.dispose();
  }
}
