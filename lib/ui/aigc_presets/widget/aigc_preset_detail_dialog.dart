import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_detail_response.dart';
import 'package:turing_art/datalayer/repository/aigc_presets/aigc_presets_repository.dart';
import 'package:turing_art/providers/aigc_preset_detail_polling_provider.dart';
import 'package:turing_art/ui/aigc_presets/viewmodel/aigc_preset_detail_view_model.dart';
import 'package:turing_art/ui/aigc_presets/widget/components/error_message_listener.dart';
import 'package:turing_art/ui/aigc_presets/widget/components/preset_detail_constants.dart';
import 'package:turing_art/ui/aigc_presets/widget/components/preset_detail_left_content.dart';
import 'package:turing_art/ui/aigc_presets/widget/components/preset_detail_right_content.dart';
import 'package:turing_art/ui/aigc_presets/widget/preview_image_widget.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';

/// AIGC 预设详情弹窗
class AigcPresetDetailDialog extends StatefulWidget {
  // 通知外层刷新列表的回调
  final VoidCallback? onRefresh;

  const AigcPresetDetailDialog({
    super.key,
    this.onRefresh,
  });

  static void show(
    BuildContext context, {
    required String presetId,
    AigcPcPresetsDetailResponse? initialData,
    VoidCallback? onRefresh,
  }) {
    PGDialog.showCustomDialog(
        width: 654,
        height: 611,
        needBlur: false,
        tag: DialogTags.aigcPresetDetail,
        child: MultiProvider(
          providers: [
            Provider<AigcPresetDetailPollingProvider>(
              create: (context) => AigcPresetDetailPollingProvider(
                repository: context.read<AigcPresetsRepository>(),
                presetId: presetId,
              ),
              dispose: (_, service) => service.dispose(),
            ),
            ChangeNotifierProvider<AIGCPresetDetailViewModel>(
              create: (context) => AIGCPresetDetailViewModel(
                repository: context.read<AigcPresetsRepository>(),
                pollingService: context.read<AigcPresetDetailPollingProvider>(),
                presetId: presetId,
                initialData: initialData,
              ),
            ),
          ],
          child: AigcPresetDetailDialog(onRefresh: onRefresh),
        ),
        radius: 16);
  }

  @override
  State<AigcPresetDetailDialog> createState() => _AigcPresetDetailDialogState();
}

class _AigcPresetDetailDialogState extends State<AigcPresetDetailDialog> {
  // 预览相关状态
  OverlayEntry? _previewOverlay;
  final GlobalKey _gridKey = GlobalKey(); // 用于获取网格位置

  @override
  void dispose() {
    _removePreviewOverlay();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF1F1F1F),
        borderRadius: BorderRadius.circular(16),
        boxShadow: const [
          BoxShadow(
            color: Color(0x4D000000),
            blurRadius: 20,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // 错误消息监听器 - 独立处理，不影响UI重建
          const ErrorMessageListener(),
          // 主要内容区域
          Expanded(
            child: Selector<AIGCPresetDetailViewModel, bool>(
              selector: (_, viewModel) => viewModel.isLoading,
              builder: (context, isLoading, child) {
                return isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : Row(children: [
                        Container(
                          width: 192,
                          padding: const EdgeInsets.all(16),
                          child: PresetDetailLeftContent(
                            coverImageSize:
                                PresetDetailConstants.coverImageSize,
                            onExit: widget.onRefresh,
                          ),
                        ),
                        const VerticalDivider(
                            color: Color(0x1AFFFFFF), width: 1, thickness: 1),
                        Expanded(
                          child: PresetDetailRightContent(
                            gridKey: _gridKey,
                            onShowPreview: _showPreviewOverlay,
                            onRemovePreview: _removePreviewOverlay,
                            onExit: _exitDialog,
                          ),
                        ),
                      ]);
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 显示预览大图覆盖层
  void _showPreviewOverlay(BuildContext context, String imageUrl, String title,
      GlobalKey itemKey, int index) {
    _removePreviewOverlay();

    // 获取悬停项目的位置
    final RenderBox? itemRenderBox =
        itemKey.currentContext?.findRenderObject() as RenderBox?;
    if (itemRenderBox == null) {
      return;
    }

    final Offset itemPosition = itemRenderBox.localToGlobal(Offset.zero);
    final Size itemSize = itemRenderBox.size;

    _previewOverlay = OverlayEntry(
      builder: (context) =>
          _buildPreviewOverlay(imageUrl, itemPosition, itemSize),
    );

    Overlay.of(context).insert(_previewOverlay!);
  }

  void _exitDialog() {
    PGDialog.dismiss(tag: DialogTags.aigcPresetDetail);
    if (context.read<AIGCPresetDetailViewModel>().hasEffectChanged) {
      widget.onRefresh?.call();
    }
  }

  /// 移除预览覆盖层
  void _removePreviewOverlay() {
    _previewOverlay?.remove();
    _previewOverlay = null;
  }

  /// 构建预览覆盖层
  Widget _buildPreviewOverlay(
      String imageUrl, Offset itemPosition, Size itemSize) {
    // 计算预览弹窗的位置：在悬停项目右侧28px处
    final double previewLeft = itemPosition.dx + itemSize.width + 28;
    final double previewTop = itemPosition.dy;

    return Positioned(
      left: previewLeft,
      top: previewTop,
      child: PreviewImageWidget(
        imageUrl: imageUrl,
      ),
    );
  }
}
