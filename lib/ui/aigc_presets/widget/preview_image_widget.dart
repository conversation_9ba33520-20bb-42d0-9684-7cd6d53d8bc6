import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:turing_art/widgets/common/cached_image_widget.dart';

/// 预览图片组件，支持根据图片尺寸自动缩放
class PreviewImageWidget extends StatefulWidget {
  final String imageUrl;

  const PreviewImageWidget({
    super.key,
    required this.imageUrl,
  });

  @override
  State<PreviewImageWidget> createState() => _PreviewImageWidgetState();
}

class _PreviewImageWidgetState extends State<PreviewImageWidget> {
  static const double _maxPreviewImageSize = 400.0; // 预览图片最大尺寸

  Size? _imageSize;
  bool _isLoading = true;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _loadImageSize();
  }

  /// 加载图片并获取其尺寸
  Future<void> _loadImageSize() async {
    try {
      final ImageProvider imageProvider =
          CachedNetworkImageProvider(widget.imageUrl);
      final ImageStream stream =
          imageProvider.resolve(ImageConfiguration.empty);

      stream.addListener(
          ImageStreamListener((ImageInfo info, bool synchronousCall) {
        if (mounted) {
          setState(() {
            _imageSize = Size(
              info.image.width.toDouble(),
              info.image.height.toDouble(),
            );
            _isLoading = false;
          });
        }
      }, onError: (exception, stackTrace) {
        if (mounted) {
          setState(() {
            _hasError = true;
            _isLoading = false;
          });
        }
      }));
    } catch (e) {
      if (mounted) {
        setState(() {
          _hasError = true;
          _isLoading = false;
        });
      }
    }
  }

  /// 计算预览尺寸，最大400x400px，保持宽高比
  Size _calculatePreviewSize() {
    if (_imageSize == null) {
      return const Size(_maxPreviewImageSize, _maxPreviewImageSize); // 默认尺寸
    }

    final double imageWidth = _imageSize!.width;
    final double imageHeight = _imageSize!.height;

    // 如果图片尺寸都小于等于最大尺寸，直接使用原尺寸
    if (imageWidth <= _maxPreviewImageSize &&
        imageHeight <= _maxPreviewImageSize) {
      return _imageSize!;
    }

    // 计算缩放比例
    final double widthRatio = _maxPreviewImageSize / imageWidth;
    final double heightRatio = _maxPreviewImageSize / imageHeight;
    final double scale = widthRatio < heightRatio ? widthRatio : heightRatio;

    return Size(
      imageWidth * scale,
      imageHeight * scale,
    );
  }

  @override
  Widget build(BuildContext context) {
    final Size previewSize = _calculatePreviewSize();

    return Container(
      width: previewSize.width,
      height: previewSize.height,
      decoration: BoxDecoration(
        color: const Color(0xFF1F1F1F),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
          color: const Color(0x33FFFFFF),
          width: 1,
        ),
        boxShadow: const [
          BoxShadow(
            color: Color(0x4D000000),
            blurRadius: 20,
            offset: Offset(0, 0),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(4),
        child: _isLoading
            ? _buildLoadingWidget(previewSize)
            : _hasError
                ? _buildErrorWidget(previewSize)
                : CachedImageWidget(
                    imageUrl: widget.imageUrl,
                    width: previewSize.width,
                    height: previewSize.height,
                    fit: BoxFit.cover,
                    placeholder: _buildLoadingWidget(previewSize),
                    errorWidget: _buildErrorWidget(previewSize),
                  ),
      ),
    );
  }

  Widget _buildLoadingWidget(Size size) {
    return Container(
      width: size.width,
      height: size.height,
      color: const Color(0xFF1F1F1F),
      child: const Center(
        child: CircularProgressIndicator(
          color: Color(0xFFF72561),
          strokeWidth: 2,
        ),
      ),
    );
  }

  Widget _buildErrorWidget(Size size) {
    return Container(
      width: size.width,
      height: size.height,
      color: const Color(0xFF363636),
      child: Image.asset(
        'assets/icons/aigc_credit_start_medium.png',
        width: 32,
        height: 32,
      ),
    );
  }
}
