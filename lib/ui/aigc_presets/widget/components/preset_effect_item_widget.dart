import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/constants/constants.dart';
import 'package:turing_art/ui/aigc_presets/model/aigc_preset_detail_ui_model.dart';
import 'package:turing_art/ui/aigc_presets/utils/aigc_request_const.dart';
import 'package:turing_art/ui/aigc_presets/viewmodel/aigc_preset_detail_view_model.dart';
import 'package:turing_art/ui/aigc_presets/widget/aigc_item_animation.dart';
import 'package:turing_art/ui/aigc_sample/widget/aigc_pc_delete_confirm_dialog.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/widgets/common/cached_image_widget.dart';

/// 单个创意项目组件
class PresetEffectItemWidget extends StatefulWidget {
  final String groupId;
  final AIGCPresetEffectUI effectUI;
  final double creativeItemSize;
  final Function(BuildContext, String, String, GlobalKey, int) onShowPreview;
  final VoidCallback onRemovePreview;

  const PresetEffectItemWidget({
    super.key,
    required this.groupId,
    required this.effectUI,
    required this.creativeItemSize,
    required this.onShowPreview,
    required this.onRemovePreview,
  });

  @override
  State<PresetEffectItemWidget> createState() => _PresetEffectItemWidgetState();
}

class _PresetEffectItemWidgetState extends State<PresetEffectItemWidget> {
  late final GlobalKey _itemKey;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _itemKey = GlobalKey();
  }

  @override
  Widget build(BuildContext context) {
    return _buildEffectItem(context, widget.effectUI);
  }

  Widget _buildEffectItem(BuildContext context, AIGCPresetEffectUI effectUI) {
    final hasImageUrl =
        effectUI.photoUrl != null && effectUI.photoUrl!.isNotEmpty;

    return PlatformMouseRegion(
      onEnter: (_) {
        setState(() {
          _isHovered = true;
        });
        if (hasImageUrl) {
          widget.onShowPreview(
              context, effectUI.photoUrl!, effectUI.name, _itemKey, 0);
        }
      },
      onExit: (_) {
        setState(() {
          _isHovered = false;
        });
        widget.onRemovePreview();
      },
      child: GestureDetector(
        onTap: () {
          if (effectUI.status != AigcRequestConst.completed) {
            // 仅生成成功的效果可被选中
            return;
          }
          context
              .read<AIGCPresetDetailViewModel>()
              .toggleCreativeItemSelection(widget.groupId, effectUI.effectCode);
        },
        child: Container(
          key: _itemKey,
          child: Stack(
            children: [
              // 图片容器
              _buildImageContainer(effectUI, hasImageUrl),
              // 选中按钮
              if (hasImageUrl) _buildSelectionButton(context, effectUI),
              // 删除按钮
              if (_isHovered && hasImageUrl) _buildDeleteButton(context),

              // 测试包中：直接显示错误信息
              if (isDebug &&
                  effectUI.errorMsg != null &&
                  effectUI.errorMsg!.isNotEmpty)
                _buildErrorMsgTIp(effectUI),
            ],
          ),
        ),
      ),
    );
  }

  /// 将错误信息展示出来
  Positioned _buildErrorMsgTIp(AIGCPresetEffectUI effectUI) {
    return Positioned.fill(
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Text(
          effectUI.errorMsg!,
          style: const TextStyle(
            color: Colors.red,
            fontSize: 10,
          ),
          textAlign: TextAlign.center,
          maxLines: 10,
          overflow: TextOverflow.ellipsis,
        ),
      ),
    );
  }

  Widget _getImageWidget(AIGCPresetEffectUI effectUI, bool hasImageUrl) {
    if (effectUI.status == AigcRequestConst.failed) {
      return _buildFailedPlaceholder();
    } else if (hasImageUrl) {
      return _buildImageWidget(effectUI);
    } else {
      return _buildAnimationPlaceholder();
    }
  }

  /// 构建效果生成失败的占位图
  Widget _buildFailedPlaceholder() {
    return Container(
        width: widget.creativeItemSize,
        height: widget.creativeItemSize,
        decoration: BoxDecoration(
          color: const Color(0xFF262626),
          borderRadius: BorderRadius.circular(8),
        ),
        child:
            Image.asset('assets/icons/icon_exclamation_mark.png', scale: 1.2));
  }

  /// 构建图片容器
  Widget _buildImageContainer(AIGCPresetEffectUI effectUI, bool hasImageUrl) {
    return SizedBox(
      width: widget.creativeItemSize,
      height: widget.creativeItemSize,
      child: Stack(
        children: [
          _getImageWidget(effectUI, hasImageUrl),

          // 边框层 - 只在选中时显示
          if (effectUI.isSelected) ...[
            // 外层红色边框
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: const Color(0xFFF72561), width: 2),
                ),
              ),
            ),
            // 内层黑色边框
            Positioned(
              left: 2,
              top: 2,
              right: 2,
              bottom: 2,
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(color: Colors.black, width: 1),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildAnimationPlaceholder() {
    return ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: AigcItemAnimation(
          child: Image.asset(
            'assets/icons/aigc_presets_loading_icon.png',
            width: widget.creativeItemSize,
            height: widget.creativeItemSize,
            fit: BoxFit.cover,
          ),
        ));
  }

  CachedImageWidget _buildImageWidget(AIGCPresetEffectUI effectUI) {
    return CachedImageWidget(
      imageUrl: effectUI.thumbUrl ?? '',
      width: widget.creativeItemSize,
      height: widget.creativeItemSize,
      radius: 8,
      fit: BoxFit.cover,
    );
  }

  /// 构建选中按钮
  Widget _buildSelectionButton(
      BuildContext context, AIGCPresetEffectUI effectUI) {
    final isSelected = effectUI.isSelected;

    return Positioned(
      top: 8,
      left: 8,
      child: Container(
        width: 20,
        height: 20,
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFF72561) : const Color(0x99000000),
          border: isSelected
              ? null
              : Border.all(
                  color: const Color(0xCCFFFFFF),
                  width: 1.5,
                ),
          borderRadius: BorderRadius.circular(4),
        ),
        child: effectUI.isSelected
            ? Image.asset('assets/icons/aigc_option_checked.png',
                width: 20, height: 20, color: Colors.white)
            : null,
      ),
    );
  }

  /// 构建删除按钮
  Widget _buildDeleteButton(BuildContext context) {
    return Positioned(
      bottom: 8,
      right: 8,
      child: GestureDetector(
        onTap: () {
          // 先移除预览overlay
          widget.onRemovePreview();

          // 弹出确认删除弹窗
          _showDeleteConfirmDialog(context);
        },
        child: Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            color: const Color(0xCC1F1F1F),
            borderRadius: BorderRadius.circular(4),
            border: Border.all(
              color: const Color(0x1AFFFFFF),
              width: 0.5,
            ),
          ),
          child: Image.asset(
            'assets/icons/aigc_delete.png',
            width: 20,
            height: 20,
          ),
        ),
      ),
    );
  }

  void _showDeleteConfirmDialog(BuildContext context) {
    // 弹出确认删除弹窗
    AigcPcDeleteConfirmDialog.show(
      context,
      title: '确定删除该创意效果？',
      content: '删除后将无法找回。',
      onConfirm: () async {
        PGDialog.dismiss(tag: DialogTags.aigcPcDeleteConfirm);
        // 执行删除操作
        await context
            .read<AIGCPresetDetailViewModel>()
            .deleteEffect(widget.groupId, widget.effectUI.effectCode);
      },
    );
  }
}
