import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/ui/aigc_presets/model/aigc_preset_detail_ui_model.dart';
import 'package:turing_art/ui/aigc_presets/viewmodel/aigc_preset_detail_view_model.dart';
import 'package:turing_art/ui/aigc_presets/widget/components/preset_detail_group_item_widget.dart';

/// 创意网格组件
class PresetDetailGridWidget extends StatefulWidget {
  final GlobalKey gridKey;
  final Function(BuildContext, String, GlobalKey, int) onShowPreview;
  final VoidCallback onRemovePreview;
  final VoidCallback onExit;

  const PresetDetailGridWidget({
    super.key,
    required this.gridKey,
    required this.onShowPreview,
    required this.onRemovePreview,
    required this.onExit,
  });

  @override
  State<PresetDetailGridWidget> createState() => _PresetDetailGridWidgetState();
}

class _PresetDetailGridWidgetState extends State<PresetDetailGridWidget> {
  late final ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Selector<AIGCPresetDetailViewModel, List<AIGCPresetGroupUI>>(
      selector: (_, viewModel) => viewModel.groups,
      builder: (context, presetGroups, child) {
        if (presetGroups.isEmpty) {
          widget.onExit.call();
        }
        return ScrollbarTheme(
          data: ScrollbarThemeData(
            // 滚动条拖拽区域的颜色 - 20%透明度的白色
            thumbColor: MaterialStateProperty.resolveWith((states) {
              if (states.contains(MaterialState.hovered)) {
                return const Color(0x40FFFFFF); // 悬停时稍微更明显一些 (25%透明度)
              }
              if (states.contains(MaterialState.dragged)) {
                return const Color(0x4DFFFFFF); // 拖拽时更明显 (30%透明度)
              }
              return const Color(0x33FFFFFF); // 默认20%透明度的白色
            }),
            // 滚动轨道的颜色 - 更透明
            trackColor: MaterialStateProperty.all(const Color(0x0DFFFFFF)),
            // 滚动条边框
            trackBorderColor: MaterialStateProperty.all(Colors.transparent),
            // 滚动条厚度 - 4px
            thickness: MaterialStateProperty.all(4.0),
            // 滚动条圆角
            radius: const Radius.circular(2),
            crossAxisMargin: 6.0,
            // 滚动条长度 - 135px
            minThumbLength: 135.0,
          ),
          child: Scrollbar(
            controller: _scrollController,
            thumbVisibility: false, // 不始终显示，只在需要时显示
            trackVisibility: false,
            child: ListView.separated(
                padding: const EdgeInsets.all(16),
                itemBuilder: (context, index) {
                  return PresetDetailGroupItemWidget(
                      groupUI: presetGroups[index]);
                },
                separatorBuilder: (context, index) =>
                    const SizedBox(height: 16),
                itemCount: presetGroups.length),
          ),
        );
      },
    );
  }
}
