import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/ui/aigc_presets/viewmodel/aigc_preset_detail_view_model.dart';
import 'package:turing_art/ui/aigc_presets/widget/components/preset_detail_bottom_actions.dart';
import 'package:turing_art/ui/aigc_presets/widget/components/preset_detail_constants.dart';
import 'package:turing_art/ui/aigc_presets/widget/components/preset_detail_grid_widget.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';

/// 右侧内容组件 - 独立管理状态
class PresetDetailRightContent extends StatelessWidget {
  final GlobalKey gridKey;
  final Function(BuildContext, String, String, GlobalKey, int) onShowPreview;
  final VoidCallback onRemovePreview;
  final VoidCallback onExit;

  const PresetDetailRightContent({
    super.key,
    required this.gridKey,
    required this.onShowPreview,
    required this.onRemovePreview,
    required this.onExit,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // 关闭按钮
        _buildCloseButton(),

        Column(
          children: [
            Container(
                height: 48,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                alignment: Alignment.centerLeft,
                child: Text(
                  '"已勾选"表示在「主题列表」中显示该创意效果。',
                  style: TextStyle(
                    color: const Color(0xB3FFFFFF),
                    fontSize: 12,
                    fontFamily: Fonts.defaultFontFamily,
                  ),
                )),

            Container(height: 1, color: const Color(0x1AFFFFFF)),

            // 创意图片网格
            Expanded(
              child: PresetDetailGridWidget(
                gridKey: gridKey,
                onShowPreview: onShowPreview,
                onRemovePreview: onRemovePreview,
                onExit: onExit,
              ),
            ),
            Container(height: 1, color: const Color(0x1AFFFFFF)),
            // 底部操作区域
            PresetDetailBottomActions(onExit: onExit),
          ],
        )
      ],
    );
  }

  Widget _buildCloseButton() {
    return Positioned(
      top: 12,
      right: 12,
      child: GestureDetector(
        onTap: () => onExit.call(),
        child: SizedBox(
          width: 24,
          height: 24,
          child: Image.asset('assets/icons/dialog_close.png',
              width: 24, height: 24),
        ),
      ),
    );
  }
}
