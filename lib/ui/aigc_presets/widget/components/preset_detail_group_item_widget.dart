import 'package:flutter/material.dart';
import 'package:turing_art/ui/aigc_presets/model/aigc_preset_detail_ui_model.dart';
import 'package:turing_art/ui/aigc_presets/widget/components/preset_detail_constants.dart';
import 'package:turing_art/ui/aigc_presets/widget/components/preset_effect_item_widget.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';

/// 预设详情页面效果列表外层 item 组件
class PresetDetailGroupItemWidget extends StatelessWidget {
  final AIGCPresetGroupUI groupUI;
  final Function(BuildContext, String, String, GlobalKey, int) onShowPreview;
  final VoidCallback onRemovePreview;

  const PresetDetailGroupItemWidget({
    super.key,
    required this.groupUI,
    required this.onShowPreview,
    required this.onRemovePreview,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          groupUI.createAt,
          style: TextStyle(
            color: const Color(0XFF6D6D6D),
            fontSize: 12,
            fontFamily: Fonts.defaultFontFamily,
          ),
        ),
        const SizedBox(height: 4),
        Text(groupUI.groupDescription,
            style: TextStyle(
              color: const Color(0xB3FFFFFF),
              fontSize: 12,
              fontWeight: FontWeight.w500,
              fontFamily: Fonts.defaultFontFamily,
            )),
        const SizedBox(height: 8),
        _buildGridView(groupUI.effectList),
      ],
    );
  }

  /// 构建显示创意图的网格视图
  Widget _buildGridView(List<AIGCPresetEffectUI> effectList) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 8.0,
        mainAxisSpacing: 8.0,
        childAspectRatio: 1.0,
      ),
      itemCount: effectList.length,
      itemBuilder: (context, index) {
        return PresetEffectItemWidget(
          key: ValueKey('creative_item_$index'),
          groupId: groupUI.groupId,
          effectUI: effectList[index],
          creativeItemSize: PresetDetailConstants.imageContainerSize,
          onShowPreview: onShowPreview,
          onRemovePreview: onRemovePreview,
        );
      },
    );
  }


}
